from django.db import models
from config import settings
from lca.accounting.models import (
    BOUNDARIES,
    PRODUCT,
    Category,
    EmissionSource,
    Geography,
    Unit,
)
from lca.file.models import File
import uuid


class FLOW_TYPES(models.TextChoices):
    """流类型"""

    ELEMENTARY = "elementary", "基本流"
    INTERMEDIATE = "intermediate", "中间流"
    PRODUCT = "product", "产品流"
    CO_PRODUCT = "co-product", "共产品流"
    WASTE = "waste", "废物流"
    RESOURCE = "resource", "资源流"


class EMISSION_SOURCE_STATUS(models.TextChoices):
    """排放源状态"""

    ONGOING = "ongoing", "审核中"
    APPROVED = "approved", "通过"
    REJECTED = "rejected", "未通过"


# 许可类型
class LICENSE_TYPES(models.TextChoices):
    """许可类型"""

    FREE = "free", "对所有用户和适用类型免费"


class EMISSION_SOURCE_PUBLISH_STATUS(models.TextChoices):
    """排放源发布状态"""

    # 未在其他地方公布数据,曾在其他地方公布数据
    NOT_PUBLISHED = "not-published", "未在其他地方公布数据"
    PUBLISHED = "published", "曾在其他地方公布数据"


class DATABASE_IMPORT_STATUS(models.TextChoices):
    PENDING = "pending", "待处理"
    IN_PROGRESS = "in_progress", "进行中"
    SUCCESS = "success", "成功"
    FAILED = "failed", "失败"


class Flow(models.Model):
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    name = models.CharField(max_length=200, verbose_name="名称", unique=True)
    type = models.CharField(max_length=20, choices=FLOW_TYPES.choices, verbose_name="流类型")
    gwp = models.DecimalField(
        decimal_places=5,
        max_digits=20,
        verbose_name="问世气体当量",
        null=True,
        blank=True,
    )


class EmissionSourceInputMixin:
    flow = models.ForeignKey(Flow, on_delete=models.CASCADE, null=True, verbose_name="流", db_index=True)
    amount = models.DecimalField(decimal_places=5, max_digits=20, null=False, blank=False, verbose_name="数量")
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)


class EmissionSourceInput(models.Model, EmissionSourceInputMixin):
    """排放源输入"""

    emission_source = models.ForeignKey(
        EmissionSource,
        on_delete=models.CASCADE,
        null=True,
        verbose_name="排放源",
        db_index=True,
        related_name="einputs",
    )
    related_emission_source = models.ForeignKey(
        EmissionSource,
        on_delete=models.CASCADE,
        null=True,
        verbose_name="关联的排放源",
        db_index=True,
        related_name="related_emission_source",
    )


class EmissionSourceOutputMixin:
    flow = models.ForeignKey(Flow, on_delete=models.CASCADE, null=True, verbose_name="流", db_index=True)
    amount = models.DecimalField(decimal_places=5, max_digits=20, null=False, blank=False, verbose_name="数量")
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)
    description = models.CharField(max_length=255, verbose_name="描述", null=True)


class EmissionSourceOutput(models.Model, EmissionSourceOutputMixin):
    emission_source = models.ForeignKey(
        EmissionSource,
        on_delete=models.CASCADE,
        null=True,
        verbose_name="排放源",
        db_index=True,
        related_name="eoutputs",
    )


class EmissionSourceProduct(models.Model):
    """排放源产品"""

    emission_source = models.ForeignKey(EmissionSource, on_delete=models.CASCADE, verbose_name="排放源", db_index=True)
    product = models.CharField(max_length=255, verbose_name="产品", choices=PRODUCT.choices, db_index=True)


class EmissionSourceDataSetMixin:
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    name = models.CharField(max_length=255, null=False, verbose_name="名称", unique=True)
    alias = models.CharField(max_length=255, verbose_name="别名", null=True)
    year = models.CharField(max_length=255, verbose_name="时间代表性")
    geography = models.ForeignKey(
        Geography,
        on_delete=models.CASCADE,
        verbose_name="适用的地理区域",
        db_index=True,
    )
    amount = models.DecimalField(decimal_places=5, max_digits=20, null=False, blank=False, verbose_name="数量")
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)
    functional_unit = models.CharField(max_length=100, verbose_name="功能单位", null=False, blank=False)
    specs = models.CharField(max_length=100, verbose_name="产品型号", null=True)
    boundary = models.CharField(
        choices=BOUNDARIES.choices,
        max_length=50,
        verbose_name="系统边界",
        null=False,
        blank=False,
    )
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="类别", null=False, blank=False)
    technical_description = models.CharField(max_length=255, verbose_name="技术描述", null=True, blank=False)
    usage = models.CharField(max_length=255, verbose_name="产品或工艺用途", null=True, blank=False)
    flow_chart = models.ForeignKey(File, on_delete=models.SET_NULL, verbose_name="工艺流程图", null=True)
    allocation_principles = models.CharField(verbose_name="分配原则", null=True, blank=True, max_length=255)
    model_description = models.CharField(verbose_name="模型描述", null=True, blank=True, max_length=255)
    data_treatment = models.CharField(verbose_name="数据处理", null=True, blank=True, max_length=255)


class EmissionSourceDataSet(models.Model, EmissionSourceDataSetMixin):
    """ILCD  数据"""

    emission_source = models.OneToOneField(
        EmissionSource,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        db_index=True,
        related_name="dataset",
    )


class EmissionSourceManagementMixin(models.Model):
    generate_version = models.CharField(max_length=50, verbose_name="数据生成版本", null=False, blank=False)
    generate_contact = models.CharField(max_length=50, verbose_name="数据生成联系人", null=False, blank=False)
    generate_contact_detail = models.CharField(max_length=50, verbose_name="数据生成联系方式", null=False, blank=False)
    generate_create_time = models.DateTimeField(verbose_name="数据生成时间", auto_now_add=True)
    generate_update_time = models.DateTimeField(verbose_name="数据生成更新时间", auto_now=True)
    inputer_contact = models.CharField(max_length=50, verbose_name="数据录入者", null=True, blank=True)
    input_create_time = models.DateTimeField(verbose_name="数据录入时间", auto_now_add=True)
    input_contact_detail = models.CharField(max_length=50, verbose_name="数据录入联系方式", null=False, blank=False)
    approve_contact = models.CharField(max_length=50, verbose_name="数据审核者联系方式", null=True, blank=True)
    approve_contact_detail = models.CharField(max_length=50, verbose_name="数据审核联系方式", null=False, blank=False)
    approve_create_time = models.DateTimeField(verbose_name="数据审核时间", auto_now_add=True)
    update_major_count = models.IntegerField(verbose_name="大修次数", default=0)
    update_minor_count = models.IntegerField(verbose_name="小修次数", default=0)
    publish = models.CharField(max_length=255, verbose_name="发布信息", null=True)
    view = models.CharField(max_length=255, verbose_name="访问权限信息", null=True)
    owener = models.CharField(max_length=50, verbose_name="数据拥有者", null=True, blank=True)
    owener_version = models.CharField(max_length=50, verbose_name="数据拥有者版本", null=False, blank=False)
    license_type = models.CharField(max_length=255, verbose_name="许可类型", null=True)
    copyright = models.CharField(max_length=255, verbose_name="版权信息", null=True)

    class Meta:
        abstract = True


class EmissionSourceManagement(models.Model, EmissionSourceManagementMixin):
    """排放源管理信息"""

    emission_source = models.OneToOneField(
        EmissionSource,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        db_index=True,
        related_name="management",
    )


class Database(models.Model):
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    name = models.CharField(max_length=255, verbose_name="名称", unique=True)
    file = models.ForeignKey(File, on_delete=models.SET_NULL, verbose_name="文件", null=True, blank=True)
    status = models.CharField(
        max_length=255,
        verbose_name="导入状态",
        choices=DATABASE_IMPORT_STATUS.choices,
        null=False,
        blank=False,
    )
    import_time = models.DateTimeField(verbose_name="导入时间", null=True, blank=True)
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)


class DatabaseImportTask(models.Model):
    source = models.ForeignKey(Database, on_delete=models.CASCADE, verbose_name="数据库", null=False, blank=False)
    status = models.CharField(
        max_length=255,
        verbose_name="导入状态",
        choices=DATABASE_IMPORT_STATUS.choices,
        null=False,
        blank=False,
    )
    progress = models.FloatField(verbose_name="导入进度", default=0, null=False, blank=False)
    import_time = models.DateTimeField(verbose_name="导入时间", null=True, blank=True)
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)


class EmissionSourceApplication(models.Model):
    emission_source = models.ForeignKey(
        EmissionSource,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=255, verbose_name="名称", null=False, blank=False)
    amount = models.DecimalField(decimal_places=5, max_digits=20, null=False, blank=False, verbose_name="数量")
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)
    geography = models.ForeignKey(Geography, on_delete=models.CASCADE, verbose_name="适用的地理区域", null=False, blank=False)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="类别", null=False, blank=False)
    source = models.CharField(max_length=255, verbose_name="数据来源", null=False, blank=False)
    year = models.CharField(max_length=255, verbose_name="时间代表性", null=False, blank=False)
    creator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="emission_source_applications",
        verbose_name="创建者",
        null=False,
        blank=False,
    )
    approver = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name="审核者",
        related_name="emission_source_approve_applications",
        null=True,
        blank=True,
    )
    reason = models.TextField(max_length=500, verbose_name="审核未通过的原因", null=True, blank=True)
    status = models.CharField(
        max_length=255,
        verbose_name="状态",
        choices=EMISSION_SOURCE_STATUS.choices,
        null=False,
        blank=False,
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    approve_time = models.DateTimeField(verbose_name="审核时间", null=True, blank=True)

    status = models.CharField(
        max_length=255,
        verbose_name="状态",
        choices=EMISSION_SOURCE_STATUS.choices,
        null=False,
        blank=False,
        default=EMISSION_SOURCE_STATUS.ONGOING,
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)


class EmissionSourceManagementApplication(models.Model, EmissionSourceManagementMixin):
    emission_source = models.OneToOneField(
        EmissionSourceApplication,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        null=False,
        blank=True,
    )


class EmissionSourceDataSetApplication(models.Model, EmissionSourceDataSetMixin):
    emission_source = models.OneToOneField(
        EmissionSourceApplication,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        null=False,
        blank=True,
    )


class EmissionSourceInputOutputApplication(models.Model, EmissionSourceInputMixin):
    emission_source = models.ForeignKey(
        EmissionSourceApplication,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        null=False,
        blank=True,
    )
    inputs = models.JSONField(verbose_name="输入", null=False, blank=False)
    outputs = models.JSONField(verbose_name="输出", null=False, blank=False)


class EmissionSourceOutputApplication(models.Model, EmissionSourceOutputMixin):
    emission_source = models.ForeignKey(
        EmissionSourceApplication,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        null=False,
        blank=True,
    )
