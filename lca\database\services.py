from datetime import datetime
import random
from lca.accounting.models import EmissionSource
from lca.common.categoryServices import CategoryServices
from django.template.loader import render_to_string
from ninja.errors import HttpError
from lca.database.models import EmissionSourceDataSetApplication, DATABASE_IMPORT_STATUS, EMISSION_SOURCE_STATUS, Database, DatabaseImportTask, EmissionSourceApplication
from lca.database.schema import EmissionSourceInSchema
from lca.file.models import File
from django.db import transaction


class DatabaseService:
    @staticmethod
    def search(text: str = "", category_id: str | None = None, **kwargs):
        query = EmissionSource.objects.filter(**kwargs)
        if category_id:
            ids = CategoryServices.get_all_id(category_id)
            query = query.filter(category_id__in=ids)
        if text:
            query = query.filter(name__icontains=text)

        return query.all()

    @staticmethod
    def export_process_ilcd(process: EmissionSource):
        start, end = DatabaseService.get_emission_source_start_end(process.dataset.year)
        cats = []
        cat = process.dataset.category
        while cat:
            cats.insert(0, cat)
            cat = cat.parent
        data = dict(
            process=process,
            dataset=process.dataset,
            management=process.management,
            inputs=process.einputs.all(),
            outputs=process.eoutputs.all(),
            start=start.strftime("%Y"),
            end=end.strftime("%Y"),
            start_time=int(start.timestamp()) * 1000,
            end_time=int(end.timestamp()) * 1000,
            management_time=process.management.generate_create_time.isoformat(),
            cats=cats,
            create_time=process.management.generate_create_time.isoformat(),
            last_update=process.management.generate_update_time.isoformat(),
        )
        return render_to_string("process.json", data)

    @staticmethod
    def get_emission_source_start_end(year: str):
        """转换年份的开始和结束时间"""
        times = year.split("-")
        start = datetime(int(times[0]), 1, 1)
        end = datetime(int(times[len(times) - 1]), 12, 31)
        return start, end

    @staticmethod
    def import_database(name, file: File):
        # 数据库中有同名的且不为导入失败的，不可以导入
        if Database.objects.filter(name=name, status__in=[
                DATABASE_IMPORT_STATUS.PENDING,
                DATABASE_IMPORT_STATUS.IN_PROGRESS,
                DATABASE_IMPORT_STATUS.SUCCESS,
        ]).exists():
            raise HttpError(422, f"数据库中已存在同名数据{name}")
        database = Database.objects.create(name=name, file=file, status=DATABASE_IMPORT_STATUS.IN_PROGRESS)

        # 添加导入任务，进度随机0-99
        return DatabaseImportTask.objects.create(source=database, status=DATABASE_IMPORT_STATUS.IN_PROGRESS, import_time=datetime.now(), progress=random.randint(0, 99))

    @staticmethod
    def export_database(database: Database):
        return database.file

    @staticmethod
    def update_emission_source_application(application: EmissionSourceApplication, data: EmissionSourceInSchema):
        with transaction.atomic():
            application = EmissionSourceApplication.objects.select_for_update().get(pk=application.id)
            if application.status != EMISSION_SOURCE_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            EmissionSourceDataSetApplication.objects.update_or_create(emission_source=application, defaults=data.dataset.model_dump())  # type: ignore
            for key, value in data.model_dump(exclude=["id"]).items():
                setattr(application, key, value)
            application.save()
