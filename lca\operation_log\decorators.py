from functools import wraps
from typing import Any, Callable, Optional, Union

from django.http import HttpResponseBase
from django.utils import timezone

from .models import OperationLog, OperationType


OperationFuncResult = Union[tuple[OperationType, str], str]
OperationFuncType = Callable[..., OperationFuncResult]


def _parse_operation_result(result: OperationFuncResult, default_type: OperationType) -> tuple[OperationType, str]:
    """统一处理操作函数的返回值"""
    match result:
        case (OperationType(op_type), description):
            return op_type, description
        case (_, description):  # 其他错误类型
            return default_type, description
        case description:  # 纯字符串
            return default_type, description


def _get_operator_info(user) -> dict[str, Any]:
    """提取操作人信息逻辑"""
    if user.is_authenticated:
        return {"operator": user, "operator_username": user.phone, "operator_nickname": user.nickname}
    return {"operator": None, "operator_username": "", "operator_nickname": ""}


def _generate_description(operation_type: OperationType) -> str:
    """统一生成描述文本"""
    content_id = 0  # 可以根据实际情况获取真实ID
    special_cases = {
        OperationType.SET_PERMISSIONS: lambda ct, cid: f"为角色{{{ct}}}设置了{{{cid}}}权限",
        OperationType.IMPORT_DATABASE: lambda ct, cid: f"导入了数据库{{{ct}}}",
        OperationType.UPLOAD_FACTOR: lambda ct, cid: f"上传了因子{{{ct}}}",
        OperationType.REVIEW_FACTOR: lambda ct, cid: f"审核了因子{{{ct}}}",
        OperationType.SET_PLATFORM_MODEL: lambda ct, cid: f"将序号为{{{cid}}}的核算模型设为了平台模型",
        OperationType.UNPUBLISH_INFO: lambda ct, cid: f"下架了{{{ct}}}{{{cid}}}",
        OperationType.CREATE_INFO: lambda ct, cid: f"新增了{{{ct}}}{{{cid}}}",
        OperationType.PUBLISH_INFO: lambda ct, cid: f"发布了{{{ct}}}{{{cid}}}",
    }
    return special_cases.get(operation_type, lambda ct, cid: f"{operation_type} {content_id}")(
        operation_type, content_id
    )


def _is_success_response(response) -> bool:
    """判断响应是否成功"""
    if response is None:
        return True
    if isinstance(response, HttpResponseBase):
        return response.status_code in [200, 201, 204]
    if isinstance(response, tuple):
        return response[0] in [200, 201, 204]
    if getattr(response, "status_code", 0) in [200, 201, 204]:
        return True
    return bool(response)


def save_operation_log(user, operation_type: OperationType, description: Optional[str] = None, *args, **kwargs) -> None:
    """保存操作日志的统一入口"""
    operator_data = _get_operator_info(user)
    log_description = description or _generate_description(
        operation_type,
    )

    OperationLog.objects.create(
        operation_type=operation_type, content=log_description, operation_time=timezone.now(), **operator_data
    )


def log_operation(
    operation_type: OperationType,
    description: Optional[str] = None,
    operation_func: OperationFuncType | None = None,
    run_before_view: bool = False,
):
    """操作日志装饰器工厂函数，用于记录用户操作到数据库。

    该装饰器会拦截非GET请求，在操作执行成功后记录操作日志。对于DELETE操作，
    会先调用operation_func获取操作描述后再执行原函数。

    :param operation_type: 操作类型枚举值，指定当前操作的类型（如CREATE/DELETE等）
    :param description: 可选的操作描述文本。如果不提供且非DELETE操作，
                       会根据operation_type自动生成描述
    :param operation_func: 可选的操作函数，用于动态生成操作描述。函数返回值应为:
                          - tuple[OperationType, str]: 包含操作类型和描述
                          - str: 仅操作描述（操作类型使用参数传入的operation_type）
    :param run_before_view: 是否在view_func执行前运行operation_func (默认False)
    :example:
        >>> @log_operation(OperationType.CREATE, "创建新项目")
        ... def create_project(request):
        ...     pass
        >>>
        >>> @log_operation(OperationType.DELETE, operation_func=delete_function)
        ... def delete_item(request, item_id):
        ...     pass
    """

    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            if request.method == "GET":
                return view_func(request, *args, **kwargs)
            # 删除的要提前获取逻辑数据
            current_type, current_desc = operation_type, description
            if not description and operation_func and run_before_view:
                current_type, current_desc = _parse_operation_result(operation_func(*args, **kwargs), operation_type)
            response = view_func(request, *args, **kwargs)
            if _is_success_response(response):
                if not description and operation_func and not run_before_view:
                    current_type, current_desc = _parse_operation_result(
                        operation_func(*args, **kwargs), operation_type
                    )
                save_operation_log(request.user, current_type, current_desc, *args, **kwargs)
            return response

        return wrapped_view

    return decorator
