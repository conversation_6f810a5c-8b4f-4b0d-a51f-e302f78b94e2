from datetime import date

from ninja import Query, Router
from ninja.pagination import paginate

from infra.PageNumberPagination import PageNumberPagination
from lca.admin.operation_log.schema import OperationLogOutSchema
from lca.operation_log.services import OperationLogService


router = Router(tags=["后台-日志管理"])


@router.get("", summary="获取操作日志列表", response=list[OperationLogOutSchema], operation_id="listOperationLog")
@paginate(PageNumberPagination)
def list_operation_log(
    request,
    operator_nickname: str = Query(None, title="操作人昵称"),
    start_time: date = Query(None, title="开始时间"),
    end_time: date = Query(None, title="结束时间"),
):
    return OperationLogService.list_operation_log(operator_nickname, start_time, end_time)
