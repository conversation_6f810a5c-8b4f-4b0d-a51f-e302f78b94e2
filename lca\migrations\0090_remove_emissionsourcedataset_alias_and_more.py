# Generated by Django 5.2.1 on 2025-07-23 05:48

import django.db.models.deletion
import lca.database.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0089_rename_import_status_database_status_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='alias',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='allocation_principles',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='boundary',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='category',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='data_treatment',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='flow_chart',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='functional_unit',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='geography',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='model_description',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='name',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='specs',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='technical_description',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='unit',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='usage',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='uuid',
        ),
        migrations.RemoveField(
            model_name='emissionsourcedataset',
            name='year',
        ),
        migrations.RemoveField(
            model_name='emissionsourceinput',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='emissionsourceinput',
            name='flow',
        ),
        migrations.RemoveField(
            model_name='emissionsourceinput',
            name='unit',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='approve_contact',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='approve_contact_detail',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='approve_create_time',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='copyright',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='generate_contact',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='generate_contact_detail',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='generate_create_time',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='generate_update_time',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='generate_version',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='input_contact_detail',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='input_create_time',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='inputer_contact',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='license_type',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='owener',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='owener_version',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='publish',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='update_major_count',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='update_minor_count',
        ),
        migrations.RemoveField(
            model_name='emissionsourcemanagement',
            name='view',
        ),
        migrations.RemoveField(
            model_name='emissionsourceoutput',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='emissionsourceoutput',
            name='description',
        ),
        migrations.RemoveField(
            model_name='emissionsourceoutput',
            name='flow',
        ),
        migrations.RemoveField(
            model_name='emissionsourceoutput',
            name='unit',
        ),
        migrations.CreateModel(
            name='EmissionSourceApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='名称')),
                ('amount', models.DecimalField(decimal_places=5, max_digits=20, verbose_name='数量')),
                ('source', models.CharField(max_length=255, verbose_name='数据来源')),
                ('year', models.CharField(max_length=255, verbose_name='时间代表性')),
                ('reason', models.TextField(blank=True, max_length=500, null=True, verbose_name='审核未通过的原因')),
                ('approve_time', models.DateTimeField(blank=True, null=True, verbose_name='审核时间')),
                ('status', models.CharField(choices=[('ongoing', '审核中'), ('approved', '通过'), ('rejected', '未通过')], default='ongoing', max_length=255, verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='emission_source_approve_applications', to=settings.AUTH_USER_MODEL, verbose_name='审核者')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.category', verbose_name='类别')),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emission_source_applications', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('emission_source', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsource', verbose_name='排放源')),
                ('geography', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.geography', verbose_name='适用的地理区域')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.unit', verbose_name='单位')),
            ],
        ),
        migrations.CreateModel(
            name='EmissionSourceDataSetApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emission_source', models.OneToOneField(blank=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsourceapplication', verbose_name='排放源')),
            ],
            bases=(models.Model, lca.database.models.EmissionSourceDataSetMixin),
        ),
        migrations.CreateModel(
            name='EmissionSourceInputOutputApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inputs', models.JSONField(verbose_name='输入')),
                ('outputs', models.JSONField(verbose_name='输出')),
                ('emission_source', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsourceapplication', verbose_name='排放源')),
            ],
            bases=(models.Model, lca.database.models.EmissionSourceInputMixin),
        ),
        migrations.CreateModel(
            name='EmissionSourceManagementApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emission_source', models.OneToOneField(blank=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsourceapplication', verbose_name='排放源')),
            ],
            bases=(models.Model, lca.database.models.EmissionSourceManagementMixin),
        ),
        migrations.CreateModel(
            name='EmissionSourceOutputApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emission_source', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsourceapplication', verbose_name='排放源')),
            ],
            bases=(models.Model, lca.database.models.EmissionSourceOutputMixin),
        ),
    ]
