from typing import List, Optional
from ninja import Query, Router
from ninja.pagination import paginate

from infra.PageNumberPagination import PageNumberPagination
from lca.operation_log.decorators import log_operation
from lca.operation_log.models import OperationType
from lca.resource_center.models import InformationStatus
from lca.resource_center.schema import (
    StandardSpecificationOutSchema,
    StandardSpecificationSchema,
)
from lca.resource_center.services import ResourceCenterLogService, ResourceCenterService


router = Router(tags=["后台-资料库-标准规范"])


@router.get(
    "",
    response=List[StandardSpecificationOutSchema],
    summary="获取标准规范列表",
    operation_id="listStandardSpecification",
)
@paginate(PageNumberPagination)
def list_standard_specification_policies(
    request,
    status: InformationStatus = Query(..., title="内容状态"),
    title: Optional[str] = Query(None, title="标准标题"),
    standard_number: Optional[str] = Query(None, title="标准号"),
    standard_scope: Optional[str] = Query(None, title="标准范围"),
):
    return ResourceCenterService.list_standard_specification(status, title, standard_number, standard_scope)


@router.post(
    "",
    response={201: StandardSpecificationOutSchema},
    summary="创建标准规范",
    operation_id="createStandardSpecification",
)
@log_operation(OperationType.CREATE, operation_func=ResourceCenterLogService.create_or_edit_standard_operation)
def create_standard_specification(request, payload: StandardSpecificationSchema):
    return 201, ResourceCenterService.create_standard_specification(payload)


@router.get(
    "/{id_}",
    response=StandardSpecificationOutSchema,
    summary="获取标准规范详情",
    operation_id="getStandardSpecification",
)
def get_standard_specification(request, id_: int):
    return ResourceCenterService.get_detail(id_)


@router.put(
    "/{id_}",
    response=StandardSpecificationSchema,
    summary="更新标准规范",
    operation_id="updateStandardSpecification",
)
@log_operation(OperationType.EDIT, operation_func=ResourceCenterLogService.create_or_edit_standard_operation)
def update_standard_specification(request, id_: int, payload: StandardSpecificationSchema):
    standard = ResourceCenterService.update_standard_specification(id_, payload)
    return standard


@router.delete(
    "/{id_}",
    response={204: None},
    summary="删除标准规范",
    operation_id="deleteStandardSpecification",
)
@log_operation(OperationType.DELETE, operation_func=ResourceCenterLogService.delete_operation, run_before_view=True)
def delete_standard_specification(request, id_: int):
    ResourceCenterService.delete(id_)
    return 204, None


@router.put(
    "/{id_}/publish",
    response={200: None},
    summary="发布标准规范",
    operation_id="publishStandardSpecification",
)
@log_operation(OperationType.PUBLISH_INFO, operation_func=ResourceCenterLogService.publish_operation)
def publish_standard_specification(request, id_: int):
    ResourceCenterService.publish(id_)


@router.put(
    "/{id_}/unpublish",
    response={200: None},
    summary="下架标准规范",
    operation_id="unpublishStandardSpecification",
)
@log_operation(
    OperationType.UNPUBLISH_INFO, operation_func=ResourceCenterLogService.unpublish_operation, run_before_view=True
)
def unpublish_standard_specification(request, id_: int):
    ResourceCenterService.unpublish(id_)
